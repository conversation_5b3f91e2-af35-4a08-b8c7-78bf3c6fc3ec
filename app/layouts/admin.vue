<script setup lang="ts">
import UnsupportedResolution from '~/components/general/UnsupportedResolution.vue'
import { useScreenRequirement } from '~/composables/utils/useScreenRequirement'

const open = ref(false)
const { t } = useI18n()

const links = [[{
  id: 'master-data',
  label: t('admin.master_data'),
  icon: 'i-lucide-file',
  to: '/admin/master-data',
}, {
  id: 'channels',
  label: t('admin.channels'),
  icon: 'i-lucide-send',
  to: '/admin/channels',
}, {
  id: 'integrations',
  label: t('admin.integrations'),
  icon: 'i-lucide-link',
  to: '/admin/integrations',
}, {
  label: t('admin.webhooks'),
  icon: 'i-lucide-webhook',
  to: '/admin/webhooks',
}, {
  label: t('admin.api_clients'),
  icon: 'i-lucide-code',
  to: '/admin/api-clients',
}, {
  id: 'import-profiles',
  label: t('admin.import_profiles'),
  icon: 'i-lucide-file-input',
  to: '/admin/import-profiles',
}, {
  id: 'export-profiles',
  label: t('admin.export_profiles'),
  icon: 'i-lucide-file-output',
  to: '/admin/export-profiles',
}, {
  id: 'users',
  label: t('admin.users'),
  icon: 'i-lucide-users',
  to: '/admin/users',
}, {
  id: 'logs',
  label: t('admin.system_logs'),
  icon: 'i-lucide-clipboard',
  to: '/admin/logs',
}, {
  id: 'settings',
  label: t('admin.settings'),
  icon: 'i-lucide-settings',
  to: '/admin/settings',
}], [
  {
    label: t('navigation.back_to_portal'),
    icon: 'i-ph-sign-out',
    to: '/',
  },
]]

const { showResolutionWarning } = useScreenRequirement()
</script>

<template>
  <UDashboardGroup>
    <UDashboardSidebar
      v-model:open="open"
      collapsible
      resizable
      class="bg-neutral-50"
    >
      <template #header="{ collapsed }">
        <div class="flex justify-center w-full my-4">
          <img v-if="!collapsed" src="~/assets/img/hyperfox.svg" alt="Hyperfox" class="w-36">
          <img v-if="collapsed" src="~/assets/img/hyperfox-logo.svg" alt="Hyperfox">
        </div>
      </template>

      <template #default="{ collapsed }">
        <UNavigationMenu
          :collapsed="collapsed"
          :items="links[0]"
          orientation="vertical"
        />

        <UNavigationMenu
          :collapsed="collapsed"
          :items="links[1]"
          orientation="vertical"
          class="mt-auto"
        />
      </template>
    </UDashboardSidebar>
    <slot />
    <UnsupportedResolution v-if="showResolutionWarning" />
  </UDashboardGroup>
</template>
