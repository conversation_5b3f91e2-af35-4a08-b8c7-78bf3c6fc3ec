<script setup lang="ts">
import type { MetricData } from '~/utils/types/api/api'
import { DateFormatter, getLocalTimeZone } from '@internationalized/date'
import { useDashboardQuery } from '~/api/dashboard/queries/useDashboardQueries'

import { convertToCalendarDate } from '~/utils/dates/calender-date.converter'
import { MetricsPeriod } from '~/utils/types/api/api'

const { t } = useI18n()

definePageMeta({
  middleware: ['auth'],
})

const TIME_DEPENDENT_METRIC_TYPES = [
  'emails-received',
  'emails-processed',
  'orders-sent-to-integration',
  'order-lines-processed',
]

function isTimeDependentMetric(metric: MetricData): boolean {
  return TIME_DEPENDENT_METRIC_TYPES.includes(metric.type)
}

function isAutoProcess() {
  const { settings } = useSettingsStore()
  return settings?.autoProcess === true
}

function isAutoProcessAvailable(metric: MetricData): boolean {
  return metric.type !== 'orders-to-process' || !isAutoProcess()
}

function nonMetricSkeletonLength(): number {
  return isAutoProcess() ? 1 : 2
}

const dateFormatter = new DateFormatter('en-US', { dateStyle: 'medium' })

const dayjs = useDayjs()
const today = dayjs().tz()

const dateRange = shallowRef({
  start: convertToCalendarDate(today.startOf('month').toDate()),
  end: convertToCalendarDate(today.endOf('month').toDate()),
})

const selectedPeriod: Ref<MetricsPeriod> = ref(MetricsPeriod.CURRENT_MONTH)
const { data: dashboardData, isLoading: isDashboardLoading } = useDashboardQuery(selectedPeriod, dateRange)

const metrics = computed(() => dashboardData.value?.metrics || [])

const timeDependentMetrics = computed(() =>
  metrics.value.filter(metric => isTimeDependentMetric(metric) && isAutoProcessAvailable(metric)),
)
const nonTimeDependentMetrics = computed(() =>
  metrics.value.filter(metric => !isTimeDependentMetric(metric) && isAutoProcessAvailable(metric)),
)

function metricCardClicked(metric: MetricData) {
  const navigationLink = metric.type === 'orders-to-validate'
    ? '/validate'
    : '/to-process'

  navigateTo(navigationLink)
}

const periods = computed(() => [
  { label: t('dashboard.periods.today'), value: MetricsPeriod.TODAY },
  { label: t('dashboard.periods.last_7_days'), value: MetricsPeriod.LAST_7_DAYS },
  { label: t('dashboard.periods.current_month'), value: MetricsPeriod.CURRENT_MONTH },
  { label: t('dashboard.periods.custom'), value: MetricsPeriod.CUSTOM },
])

function selectPeriod(period: MetricsPeriod) {
  selectedPeriod.value = period
}

const { environment } = useSettingsStore()
</script>

<template>
  <UContainer class="pb-30">
    <UPageBody>
      <DashboardGettingStarted v-if="environment?.isStock" class="mb-14" />
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        <template v-if="isDashboardLoading">
          <DashboardMetricCardSkeleton
            v-for="index in nonMetricSkeletonLength()"
            :key="`non-time-skeleton-${index}`"
            class="cursor-pointer hover:shadow transition-shadow duration-200"
          />
        </template>
        <template v-else>
          <DashboardMetricCard
            v-for="(metric, index) in nonTimeDependentMetrics"
            :key="`non-time-metric-${index}`"
            :metric="metric"
            class="cursor-pointer hover:shadow transition-shadow duration-200"
            @click="metricCardClicked(metric)"
          />
        </template>
      </div>

      <div class="space-y-8">
        <div class="flex flex-col sm:flex-row">
          <UButtonGroup orientation="horizontal">
            <UButton
              v-for="period in periods"
              :key="period.value"
              :variant="selectedPeriod === period.value ? 'subtle' : 'outline'"
              :label="period.label"
              @click="selectPeriod(period.value)"
            />
          </UButtonGroup>
          <div v-if="selectedPeriod === MetricsPeriod.CUSTOM" class="ml-0 mt-2 sm:ml-4 sm:mt-0">
            <UPopover>
              <UButton variant="outline" icon="i-lucide-calendar">
                {{ dateFormatter.format(dateRange.start.toDate(getLocalTimeZone())) }} -
                {{ dateFormatter.format(dateRange.end.toDate(getLocalTimeZone())) }}
              </UButton>

              <template #content>
                <UCalendar
                  v-model="dateRange"
                  class="p-2"
                  :number-of-months="2"
                  range
                />
              </template>
            </UPopover>
          </div>
        </div>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <template v-if="isDashboardLoading">
            <DashboardMetricCardSkeleton
              v-for="index in 4"
              :key="`time-skeleton-${index}`"
            />
          </template>
          <template v-else>
            <DashboardMetricCard
              v-for="(metric, index) in timeDependentMetrics"
              :key="`time-metric-${index}`"
              :metric="metric"
            />
          </template>
        </div>
      </div>
    </UPageBody>
  </UContainer>
</template>
