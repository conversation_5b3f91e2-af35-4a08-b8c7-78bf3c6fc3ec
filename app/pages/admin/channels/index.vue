<script setup lang="ts">
import type { ChannelData } from '~/utils/types/api/api'
import { FormsChannelsUpsertMynuma } from '#components'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const overlay = useOverlay()
const { t } = useI18n()

const channelsStore = useChannelsStore()

// Modals
const upsertModal = overlay.create(FormsChannelsUpsertMynuma)

onMounted(async () => {
  await channelsStore.hydrateAvailable()
  await channelsStore.hydrate()
})

function addChannel() {
  upsertModal.open({
    data: undefined,
  })
}

function configureChannel(channel: ChannelData) {
  upsertModal.open({
    data: channel,
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('channels.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UPageCard :title="t('channels.available_channels')" variant="naked" />
      <UPageCard variant="subtle" :ui="{ container: 'divide-y divide-(--ui-border)' }">
        <div
          v-for="(channel, index) in channelsStore.availableChannels"
          :key="index"
          class="flex items-center justify-between not-last:pb-4 gap-2"
        >
          <div>
            <p class="font-bold">
              {{ channel.name }}
            </p>
          </div>
          <UButton
            variant="outline"
            leading-icon="i-ph-plus"
            :label="t('channels.add_channel')"
            @click="addChannel()"
          />
        </div>
      </UPageCard>
      <UPageCard :title="t('channels.configured_channels')" variant="naked" />
      <UPageCard variant="subtle" :ui="{ container: 'divide-y divide-(--ui-border)' }">
        <div
          v-for="channel in channelsStore.channels"
          :key="channel.id"
          class="flex items-center justify-between not-last:pb-4 gap-2"
        >
          <div class="flex flex-col gap-2">
            <p class="font-bold flex items-center">
              {{ channelsStore.getAvailableChannelByProvider(channel.type)?.name }}
              <UBadge
                :color="channel.is_active ? `success` : `error`"
                class="ml-2"
                variant="subtle"
              >
                {{ channel.is_active ? t('channels.active') : t('channels.inactive') }}
              </UBadge>
            </p>
            <p class="text-sm text-gray-500">
              {{ channel.label }}
            </p>
          </div>
          <UButton
            variant="outline"
            leading-icon="i-ph-gear"
            :label="t('channels.configure')"
            @click="configureChannel(channel)"
          />
        </div>
        <p v-if="!channelsStore.channels?.length" class="p-4 text-center text-gray-500">
          {{ t('channels.no_configured_channels') }}
        </p>
      </UPageCard>
    </template>
  </UDashboardPanel>
</template>
