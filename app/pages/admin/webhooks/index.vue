<script setup lang="ts">
import { useDeleteWebhookMutation, useWebhooksQuery } from '~/api/webhooks'
import WebhooksList from '~/components/webhooks/WebhooksList.vue'

definePageMeta({
  layout: 'admin',
})

// Use real webhook API
const {
  data: webhooks,
  isFetching: isFetchingWebhooks,
} = useWebhooksQuery()

const toast = useToast()
const { t } = useI18n()

const router = useRouter()
function configureWebhook(webhook: any) {
  router.push({
    path: `/admin/webhooks/${webhook.id}`,
  })
}
const deleteWebhookMutation = useDeleteWebhookMutation()

async function deleteWebhook(webhookId: string) {
  try {
    await deleteWebhookMutation.mutateAsync(webhookId)
    toast.add({
      title: t('webhooks.messages.deleted_success'),
      description: t('webhooks.messages.deleted_description'),
      color: 'success',
    })
  }
  catch {
    toast.add({
      title: t('webhooks.messages.delete_error'),
      description: t('webhooks.messages.delete_error_description'),
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('webhooks.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="mb-6 flex justify-between items-end">
        <div>
          <h1 class="text-2xl">
            {{ t('webhooks.title') }}
          </h1>
          <p class="text-gray-500 mt-3">
            {{ t('webhooks.subtitle') }}
          </p>
        </div>
        <UButton
          :label="t('webhooks.create_button')"
          size="lg"
          @click="$router.push('/admin/webhooks/new')"
        />
      </div>
      <WebhooksList
        v-if="!isFetchingWebhooks && webhooks"
        :webhooks="webhooks"
        @configure-webhook="configureWebhook"
        @delete-webhook="deleteWebhook"
      />
    </template>
  </UDashboardPanel>
</template>
