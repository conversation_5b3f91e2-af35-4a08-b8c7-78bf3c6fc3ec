<script setup lang="ts">
import type { UpdateSettingsData } from '~/utils/types/api/api'

const settingsStore = useSettingsStore()

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})
const toast = useToast()
const { t } = useI18n()

// Component initialize
const loading = ref(false)
const updateSettingsData = ref<UpdateSettingsData>({
  autoProcess: settingsStore.settings?.autoProcess ?? false,
  renderOfficeDocuments: settingsStore.settings?.renderOfficeDocuments ?? false,
})

// Functions
async function updateSettings() {
  try {
    loading.value = true

    await settingsStore.update(updateSettingsData.value)

    toast.add({
      title: t('settings.messages.updated_success'),
      color: 'success',
    })
  }
  catch (error: any) {
    toast.add({
      title: t('settings.messages.update_error'),
      description: error.data?.message || t('settings.messages.generic_error'),
      color: 'error',
    })
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('settings.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="flex flex-col gap-4">
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              {{ t('settings.product') }}
            </h4>
          </div>
          <div>{{ settingsStore.settings?.recipeModel }}</div>
        </div>
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              {{ t('settings.master_data_version') }}
            </h4>
          </div>
          <div>{{ settingsStore.settings?.recipeVersion }}</div>
        </div>
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              {{ t('settings.auto_process_orders') }}
            </h4>
            <p class="text-sm text-neutral-500">
              {{ t('settings.auto_process_description') }}
            </p>
          </div>
          <USwitch
            v-model="updateSettingsData.autoProcess"
            :disabled="loading"
            @change="updateSettings"
          />
        </div>
        <div class="flex items-center justify-between">
          <div>
            <h4 class="font-medium">
              {{ t('settings.render_office_documents') }}
            </h4>
            <p class="text-sm text-neutral-500">
              {{ t('settings.render_office_description') }}
            </p>
          </div>
          <USwitch
            v-model="updateSettingsData.renderOfficeDocuments"
            :disabled="loading"
            @change="updateSettings"
          />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
