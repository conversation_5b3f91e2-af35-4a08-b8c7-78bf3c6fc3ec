<script setup lang="ts">
import type { AssetTypeData } from '~/utils/types/api/api'
import {
  FormsAssetTypeAddAssetTypeFieldForm,
  FormsAssetTypeAddAssetTypeIndexForm,
  FormsAssetTypeAddAssetTypeRelationshipForm,
  FormsAssetTypeUpsertAssetTypeFormFieldForm,
  FormsAssetTypeUpsertAssetTypeTableFieldForm,
} from '#components'
import { useGenerateAssetTypeFormFieldsMutation } from '~/api/asset-type-form-fields/mutations/useAssetTypeFormFieldMutations'

const TABS = {
  FIELDS: 'fields',
  RELATIONSHIPS: 'relationships',
  INDEXES: 'indexes',
  FORM_FIELDS: 'formFields',
  TABLE_FIELDS: 'tableFields',
} as const

type TabValue = typeof TABS[keyof typeof TABS]

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const route = useRoute()
const router = useRouter()
const overlay = useOverlay()
const toast = useToast()
const { t } = useI18n()

const assetTypeStore = useAssetTypeStore()

const assetType = ref<AssetTypeData | undefined>()
const assetTypeId = route.params.id as string

const currentTab = ref<TabValue>(
  Object.values(TABS).includes(route.query.tab as TabValue)
    ? route.query.tab as TabValue
    : TABS.FIELDS,
)

// Modals
const fieldModal = overlay.create(FormsAssetTypeAddAssetTypeFieldForm)
const relationshipModal = overlay.create(FormsAssetTypeAddAssetTypeRelationshipForm)
const indexModal = overlay.create(FormsAssetTypeAddAssetTypeIndexForm)
const formFieldModal = overlay.create(FormsAssetTypeUpsertAssetTypeFormFieldForm)
const tableFieldModal = overlay.create(FormsAssetTypeUpsertAssetTypeTableFieldForm)

// Mutations
const generateAssetTypeFormFieldsMutation = useGenerateAssetTypeFormFieldsMutation()

onMounted(async () => {
  assetType.value = assetTypeStore.getById(assetTypeId)
})

watch(
  () => route.query.tab,
  (newTab) => {
    const validTab = Object.values(TABS).includes(newTab as TabValue)
      ? newTab as TabValue
      : TABS.FIELDS
    currentTab.value = validTab
  },
)

const buttonLabel = computed<string>(() => {
  const tabKey = `add_${currentTab.value}` as keyof typeof t
  return t(`master_data.buttons.${tabKey}`)
})

const links = computed(() => {
  return [
    {
      label: 'Fields',
      active: currentTab.value === TABS.FIELDS,
      onSelect: () => setTab(TABS.FIELDS),
    },
    {
      label: 'Relationships',
      active: currentTab.value === TABS.RELATIONSHIPS,
      onSelect: () => setTab(TABS.RELATIONSHIPS),
    },
    {
      label: 'Indexes',
      active: currentTab.value === TABS.INDEXES,
      onSelect: () => setTab(TABS.INDEXES),
    },
    {
      label: 'Form fields',
      active: currentTab.value === TABS.FORM_FIELDS,
      onSelect: () => setTab(TABS.FORM_FIELDS),
    },
    {
      label: 'Table fields',
      active: currentTab.value === TABS.TABLE_FIELDS,
      onSelect: () => setTab(TABS.TABLE_FIELDS),
    },
  ]
})

function buttonAction() {
  if (currentTab.value === TABS.FIELDS) {
    fieldModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === TABS.RELATIONSHIPS) {
    relationshipModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === TABS.INDEXES) {
    indexModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === TABS.FORM_FIELDS) {
    formFieldModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
  if (currentTab.value === TABS.TABLE_FIELDS) {
    tableFieldModal.open({
      key: assetTypeId,
      assetTypeId,
    })
  }
}

async function generateAction() {
  try {
    await generateAssetTypeFormFieldsMutation.mutateAsync(assetTypeId)
    toast.add({
      title: t('master_data.messages.form_fields_generated_success'),
      color: 'success',
    })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.form_fields_generate_error'),
      description: (e as any)?.data?.message || (e as any)?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}

function setTab(tab: TabValue) {
  currentTab.value = tab
  // Update URL query parameter without page reload
  router.push({
    query: {
      ...route.query,
      tab,
    },
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('master_data.detail_title', { label: assetType?.label })">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            v-if="currentTab === TABS.FORM_FIELDS"
            label="generate"
            leading-icon="i-ph-arrow-clockwise"
            variant="outline"
            :loading="generateAssetTypeFormFieldsMutation.isPending.value"
            :disabled="generateAssetTypeFormFieldsMutation.isPending.value"
            @click="generateAction"
          />
          <UButton
            :label="buttonLabel"
            leading-icon="i-ph-plus"
            @click="buttonAction"
          />
        </template>
      </UDashboardNavbar>
      <UDashboardToolbar>
        <UNavigationMenu :items="links" highlight class="-mx-1 flex-1" />
      </UDashboardToolbar>
    </template>
    <template #body>
      <div v-if="assetType">
        <div v-if="currentTab === TABS.FIELDS">
          <TablesAssetTypeFieldsTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === TABS.RELATIONSHIPS">
          <TablesAssetTypeRelationshipsTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === TABS.INDEXES">
          <TablesAssetTypeIndexesTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === TABS.FORM_FIELDS">
          <TablesAssetTypeFormFieldsTable :asset-type-id="assetTypeId" />
        </div>
        <div v-if="currentTab === TABS.TABLE_FIELDS">
          <TablesAssetTypeTableFieldsTable :asset-type-id="assetTypeId" />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
