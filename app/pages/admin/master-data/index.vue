<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeData } from '~/utils/types/api/api'
import { FormsAssetTypeAddAssetTypeForm, FormsAssetTypeEditAssetTypeForm, ModalsConfirmModal } from '#components'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

// Component setup
const overlay = useOverlay()
const toast = useToast()
const { t } = useI18n()

const assetTypeStore = useAssetTypeStore()
const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()

function getFieldLabelById(fieldId: string): string {
  try {
    const field = allAssetTypeFields.value?.find(f => f.id === fieldId)
    return field?.label || '---'
  }
  catch {
    return '---'
  }
}

const assetFactoryStore = useAssetFactoryStore()

const columns: BaseColumn<AssetTypeData>[] = [
  new CopyColumn<AssetTypeData>('id', 'Id'),
  new TextColumn<AssetTypeData>('label', 'Label'),
  new TextColumn<AssetTypeData>('name', t('common.fields.name')),
  new TextColumn<AssetTypeData>('table_name', t('master_data.columns.table_name')),
  new TextColumn<AssetTypeData>('display_field_id', t('master_data.columns.display_field'), false, (assetType) => {
    return getFieldLabelById(assetType?.display_field_id ?? '')
  }),
  new CheckboxColumn<AssetTypeData>('supporting_data', t('master_data.columns.reference_data')),
  new ActionColumn<AssetTypeData>(t('master_data.columns.actions'), [
    {
      label: t('common.actions.edit'),
      onClick: async (row) => {
        await navigateTo(`/admin/master-data/${row.id}`)
      },
    },
    { label: t('common.actions.settings'), onClick: row => onEdit(row) },
    { label: t('common.actions.delete'), onClick: row => onDelete(row.id) },
    { label: t('master_data.actions.clear_data'), onClick: row => onClearData(row), shouldShow: row => canClearData(row) },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const addModal = overlay.create(FormsAssetTypeAddAssetTypeForm)
const editModal = overlay.create(FormsAssetTypeEditAssetTypeForm)
const confirmModal = overlay.create(ModalsConfirmModal)

function onAdd() {
  addModal.open()
}

function onEdit(assetType: AssetTypeData) {
  editModal.open({ assetType })
}

async function onDelete(id: string) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeStore.destroy(id)
    toast.add({ title: t('master_data.messages.deleted_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.delete_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}

function canClearData(assetType: AssetTypeData) {
  return assetType.supporting_data
}

async function onClearData(assetType: AssetTypeData) {
  const overlay = confirmModal.open()
  const store = assetFactoryStore.get(assetType.table_name)()

  if (!await overlay.result)
    return

  try {
    await store.truncate()
    toast.add({ title: t('master_data.messages.data_truncated'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.truncate_error'),
      description: (e as FetchError).data?.message || JSON.stringify(e) || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('master_data.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            :label="t('master_data.add_button')"
            leading-icon="i-ph-plus"
            @click="onAdd"
          />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UTable :columns="columnConfig" :data="assetTypeStore.assetTypes" />
    </template>
  </UDashboardPanel>
</template>
