<script setup lang="ts">
import type { IntegrationData } from '~/utils/types/api/api'
import { useDeleteIntegrationMutation } from '~/api/integrations/mutations/useIntegrationMutations'
import { useAvailableIntegrationsQuery, useIntegrationsQuery } from '~/api/integrations/queries/useIntegrationQueries'

definePageMeta({
  middleware: ['auth'],
  layout: 'admin',
})

const {
  data: integrations,
  isFetching: isFetchingIntegrations,
} = useIntegrationsQuery()

const {
  data: availableIntegrations,
  isFetching: isFetchingAvailableIntegrations,
} = useAvailableIntegrationsQuery()

const router = useRouter()
const { t } = useI18n()

async function addIntegration(integration: string) {
  router.push({
    path: '/admin/integrations/new',
    query: { provider: integration },
  })
}

async function configureIntegration(integration: IntegrationData) {
  router.push({
    path: `/admin/integrations/${integration.id}`,
    query: { provider: integration.type },
  })
}

const deleteIntegrationMutation = useDeleteIntegrationMutation()

const toast = useToast()
async function deleteIntegration(integrationId: string) {
  await deleteIntegrationMutation.mutateAsync(integrationId)

  toast.add({
    title: t('integrations.messages.deleted_success'),
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('integrations.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="mb-6">
        <h1 class="text-2xl">
          {{ t('integrations.title') }}
        </h1>
        <p class="text-gray-500 mt-3">
          {{ t('integrations.subtitle') }}
        </p>
      </div>

      <IntegrationsList
        v-if="!isFetchingIntegrations && integrations"
        :integrations="integrations"
        @configure-integration="configureIntegration"
        @delete-integration="deleteIntegration"
      />

      <IntegrationsAvailable
        v-if="!isFetchingAvailableIntegrations && availableIntegrations"
        class="mt-8"
        :available-integrations="availableIntegrations"
        @add-integration="addIntegration"
      />
    </template>
  </UDashboardPanel>
</template>
