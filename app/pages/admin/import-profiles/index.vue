<script setup lang="ts">
import type { ImportProfileData } from '~/utils/types/api/api'
import { useDeleteImportProfileMutation } from '~/api/import-profiles/mutations/useImportProfileMutations'
import { useImportProfilesQuery } from '~/api/import-profiles/queries/useImportProfileQueries'
import { isImportProfileReady, isValidImportProfileStatus } from '~/utils/import-profiles/import-profile-status.checker'
import { IMPORT_PROFILE_STATUSES } from '~/utils/import-profiles/import-profile-statusses'

definePageMeta({
  layout: 'admin',
})

const {
  data: profiles,
  isFetching: isFetchingProfiles,
} = useImportProfilesQuery()

const router = useRouter()
const { t } = useI18n()

function configureProfile(profile: ImportProfileData) {
  const step = profile.status === IMPORT_PROFILE_STATUSES.WAITING_FOR_MAPPING
    ? IMPORT_PROFILE_STATUSES.MAPPING
    : profile.status

  const status = isValidImportProfileStatus(step)
    ? isImportProfileReady(profile)
      ? IMPORT_PROFILE_STATUSES.BASIC_INFORMATION
      : step
    : IMPORT_PROFILE_STATUSES.BASIC_INFORMATION

  router.push({
    path: `/admin/import-profiles/${profile.id}`,
    query: { step: status },
  })
}

const deleteImportProfile = useDeleteImportProfileMutation()

async function deleteProfile(profileId: string) {
  await deleteImportProfile.mutateAsync(profileId)

  useToast().add({
    title: t('import_profiles.messages.deleted_success'),
    description: t('import_profiles.messages.deleted_description'),
    color: 'success',
  })
}
</script>

<template>
  <UDashboardPanel>
    <template #header>
      <UDashboardNavbar :title="t('import_profiles.title')">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="mb-6 flex justify-between items-end">
        <div>
          <h1 class="text-2xl">
            {{ t('import_profiles.title') }}
          </h1>
          <p class="text-gray-500 mt-3">
            {{ t('import_profiles.subtitle') }}
          </p>
        </div>
        <UButton
          :label="t('import_profiles.create_button')"
          size="lg"
          @click="$router.push('/admin/import-profiles/new')"
        />
      </div>
      <ImportProfilesList
        v-if="!isFetchingProfiles && profiles"
        :profiles="profiles"
        @configure-profile="configureProfile"
        @delete-profile="deleteProfile"
      />
    </template>
  </UDashboardPanel>
</template>
