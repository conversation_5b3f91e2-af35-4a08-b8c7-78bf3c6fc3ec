<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type {
  AssetTypeFieldData,
  EditAssetTypeFieldData,
} from '~/utils/types/api/api'
import { useUpdateAssetTypeFieldMutation } from '~/api/asset-type-fields'

const props = defineProps({
  assetTypeField: {
    type: Object as PropType<AssetTypeFieldData>,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()
const { t } = useI18n()

const data = ref<EditAssetTypeFieldData>({
  locked: props.assetTypeField.locked ?? false,
  required: props.assetTypeField.required ?? false,
  searchable: props.assetTypeField.searchable ?? false,
  weight: props.assetTypeField.weight ?? 1,
})

const editAssetTypeFieldMutation = useUpdateAssetTypeFieldMutation()

async function onSubmit() {
  try {
    await editAssetTypeFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeField.asset_type_id,
      fieldId: props.assetTypeField.id,
      data: data.value,
    })
    emit('close', false)
    toast.add({ title: t('master_data.messages.field_updated_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.field_update_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}
</script>

<template>
  <USlideover :title="t('master_data.forms.edit_field_title', { label: assetTypeField.label })" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField :label="t('master_data.forms.labels.weight')" class="col-span-3">
            <UInput v-model="data.weight" placeholder="1" type="number" />
          </UFormField>
          <div class="col-span-9" />
          <UFormField :label="t('master_data.forms.labels.locked')" class="col-span-4">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.required')" class="col-span-4">
            <USwitch v-model="data.required" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.searchable')" class="col-span-4">
            <USwitch v-model="data.searchable" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="editAssetTypeFieldMutation.isPending.value"
          :disabled="editAssetTypeFieldMutation.isPending.value"
        >
          {{ t('master_data.forms.buttons.update') }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
