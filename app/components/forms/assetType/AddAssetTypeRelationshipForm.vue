<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeRelationshipData, CreateAssetTypeRelationshipData } from '~/utils/types/api/api'
import {

  AssetTypeRelationshipMethod,

} from '~/utils/types/api/api'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()
const { t } = useI18n()

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const isPending = ref(false)
const assetTypeRelationshipOptions = ref<AssetTypeRelationshipData[]>([])
const data = ref<CreateAssetTypeRelationshipData>({
  label: '',
  relationship_method: AssetTypeRelationshipMethod.BelongsTo,
  relationship_asset_type_id: assetTypeStore.assetTypes[0]?.id ?? '',
  inverse_relationship_id: null,
  nullable: false,
  include: false,
  relationship_class: null,
  relationship_key: null,
  locked: false,
  cascade_delete: false,
  required: false,
})

const foreignKeyRequiredOnRelationshipModel = computed(() => {
  return (
    data.value.relationship_method === AssetTypeRelationshipMethod.HasMany
    || data.value.relationship_method === AssetTypeRelationshipMethod.HasOne
  )
})

const inverseRelationshipIdModel = computed({
  get: () => data.value.inverse_relationship_id || '',
  set: (val: string) => {
    data.value.inverse_relationship_id = val || null
  },
})

async function onSubmit() {
  try {
    isPending.value = true
    await assetTypeRelationshipStore.create(props.assetTypeId, data.value)
    emit('close', false)
    toast.add({ title: t('master_data.messages.relationship_created_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.relationship_create_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}

async function updatedRelationshipMethod() {
  if (!foreignKeyRequiredOnRelationshipModel.value) {
    data.value.inverse_relationship_id = null
    return
  }

  await updateAssetTypeRelationshipOptions()
}

async function updatedAssetType() {
  await updateAssetTypeRelationshipOptions()
}

async function updateAssetTypeRelationshipOptions() {
  data.value.inverse_relationship_id = null

  assetTypeRelationshipOptions.value = assetTypeRelationshipStore.getByAssetTypeId(
    data.value.relationship_asset_type_id ?? '',
    [
      { field: 'relationship_asset_type_id', value: props.assetTypeId },
      { field: 'relationship_method', value: AssetTypeRelationshipMethod.BelongsTo },
    ],
  )
}

const relationshipAssetTypeIdModel = computed({
  get: () => data.value.relationship_asset_type_id || '',
  set: (val: string) => {
    data.value.relationship_asset_type_id = val || null
  },
})
</script>

<template>
  <USlideover :title="t('master_data.forms.add_relationship_title')" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between gap-4" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Label" class="col-span-6">
            <UInput v-model="data.label" :placeholder="t('master_data.forms.placeholders.enter_label_name')" />
          </UFormField>
          <UFormField label="Type" class="col-span-6">
            <USelect
              v-model="data.relationship_method"
              :items="Object.values(AssetTypeRelationshipMethod)"
              @change="updatedRelationshipMethod"
            />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.master_data')" class="col-span-6">
            <USelect
              v-model="relationshipAssetTypeIdModel"
              :items="assetTypeStore.assetTypes"
              value-key="id"
              label-key="label"
              :placeholder="t('master_data.forms.placeholders.select_master_data')"
              class="w-full"
              @change="updatedAssetType"
            />
          </UFormField>
          <UFormField v-if="foreignKeyRequiredOnRelationshipModel" :label="t('master_data.forms.labels.master_data_relationship')" class="col-span-6">
            <USelect
              v-model="inverseRelationshipIdModel"
              :items="assetTypeRelationshipOptions"
              value-key="id"
              label-key="label"
              :placeholder="t('master_data.forms.placeholders.select_foreign_relationship')"
              class="w-full"
            />
          </UFormField>
          <USeparator class="col-span-12" />
          <UFormField :label="t('master_data.forms.labels.nullable')" class="col-span-3">
            <USwitch v-model="data.nullable" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.include')" class="col-span-3">
            <USwitch v-model="data.include" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.locked')" class="col-span-3">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.cascade_delete')" class="col-span-3">
            <USwitch v-model="data.cascade_delete" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          {{ t('master_data.forms.buttons.create') }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
