<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { UpsertAssetTypeFieldData } from '~/utils/types/api/api'
import { useCreateAssetTypeFieldMutation } from '~/api/asset-type-fields'
import { AssetTypeFieldType } from '~/utils/types/api/api'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()
const { t } = useI18n()

const data = ref<UpsertAssetTypeFieldData>({
  label: '',
  type: AssetTypeFieldType.String,
  precision: null,
  nullable: true,
  locked: false,
  required: false,
  searchable: false,
  weight: 1,
  enum_values: [],
})

const createAssetTypeFieldMutation = useCreateAssetTypeFieldMutation()

async function onSubmit() {
  try {
    await createAssetTypeFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      data: data.value,
    })
    toast.add({ title: t('master_data.messages.field_created_success'), color: 'success' })
    emit('close', false)
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.field_create_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}
</script>

<template>
  <USlideover :title="t('master_data.forms.add_field_title')" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Label" class="col-span-6">
            <UInput v-model="data.label" :placeholder="t('master_data.forms.placeholders.enter_label_name')" />
          </UFormField>
          <UFormField label="Type" class="col-span-6">
            <USelect v-model="data.type" :items="Object.values(AssetTypeFieldType)" class="w-full" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.weight')" class="col-span-6">
            <UInput v-model="data.weight" placeholder="1" type="number" />
          </UFormField>
          <div class="col-span-6" />
          <UFormField label="Nullable" class="col-span-3">
            <USwitch v-model="data.nullable" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.locked')" class="col-span-3">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.required')" class="col-span-3">
            <USwitch v-model="data.required" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.searchable')" class="col-span-3">
            <USwitch v-model="data.searchable" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="createAssetTypeFieldMutation.isPending.value"
          :disabled="createAssetTypeFieldMutation.isPending.value"
        >
          {{ t('master_data.forms.buttons.create') }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
