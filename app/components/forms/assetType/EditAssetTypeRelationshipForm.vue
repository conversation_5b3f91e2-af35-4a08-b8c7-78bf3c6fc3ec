<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { AssetTypeRelationshipData, UpdateAssetTypeRelationshipData } from '~/utils/types/api/api'

const props = defineProps({
  assetTypeRelationship: {
    type: Object as PropType<AssetTypeRelationshipData>,
    required: true,
  },
})

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()
const { t } = useI18n()

const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const isPending = ref(false)
const data = ref<UpdateAssetTypeRelationshipData>({
  nullable: props.assetTypeRelationship.nullable ?? false,
  include: props.assetTypeRelationship.include ?? false,
  locked: props.assetTypeRelationship.locked ?? false,
  cascade_delete: props.assetTypeRelationship.cascade_delete ?? false,
  required: props.assetTypeRelationship.required ?? false,
})

async function onSubmit() {
  if (!props.assetTypeRelationship.id || !props.assetTypeRelationship.asset_type_id) {
    throw new Error('Asset type relationship ID and asset type ID are required')
  }

  try {
    isPending.value = true
    await assetTypeRelationshipStore.update(
      props.assetTypeRelationship.asset_type_id,
      props.assetTypeRelationship.id,
      data.value,
    )
    emit('close', false)
    toast.add({ title: t('master_data.messages.relationship_updated_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.relationship_update_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover :title="t('master_data.forms.edit_relationship_title', { label: assetTypeRelationship.label })" :overlay="false">
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <USeparator class="col-span-12" />
          <UFormField :label="t('master_data.forms.labels.nullable')" class="col-span-6">
            <USwitch v-model="data.nullable" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.include')" class="col-span-6">
            <USwitch v-model="data.include" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.locked')" class="col-span-6">
            <USwitch v-model="data.locked" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.cascade_delete')" class="col-span-6">
            <USwitch v-model="data.cascade_delete" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.required')" class="col-span-6">
            <USwitch v-model="data.required" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          {{ t('master_data.forms.buttons.update') }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
