<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { CreateAssetTypeData } from '~/utils/types/api/api'

const emit = defineEmits<{ close: [boolean] }>()
const toast = useToast()
const { t } = useI18n()

const assetTypeStore = useAssetTypeStore()

const isPending = ref(false)
const data = ref<CreateAssetTypeData>({
  label: '',
  auditing_enabled: false,
  supporting_data: false,
  locked: false,
})

async function onSubmit() {
  try {
    isPending.value = true

    const assetType = await assetTypeStore.create(data.value)
    await assetTypeStore.hydrate()

    emit('close', false)
    toast.add({ title: t('master_data.messages.master_data_created_success'), color: 'success' })
    await navigateTo(`/admin/master-data/${assetType.data.id}`)
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.master_data_create_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
  finally {
    isPending.value = false
  }
}
</script>

<template>
  <USlideover
    :title="t('master_data.forms.add_master_data_title')"
    :overlay="false"
  >
    <template #body>
      <form class="h-full flex flex-col justify-between" @submit.prevent="onSubmit">
        <div class="grid grid-cols-12 gap-4">
          <UFormField label="Label" class="col-span-12">
            <UInput v-model="data.label" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.auditing_enabled')" class="col-span-6">
            <USwitch v-model="data.auditing_enabled" />
          </UFormField>
          <UFormField :label="t('master_data.forms.labels.locked')" class="col-span-6">
            <USwitch v-model="data.locked" />
          </UFormField>
        </div>
        <UButton
          type="submit"
          :loading="isPending"
          :disabled="isPending"
        >
          {{ t('master_data.forms.buttons.create') }}
        </UButton>
      </form>
    </template>
  </USlideover>
</template>
