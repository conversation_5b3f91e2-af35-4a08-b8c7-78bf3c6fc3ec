<script setup lang="ts">
const { setLocale, locale } = useI18n()

const availableLocales = [
  { label: 'English', value: 'en' },
  { label: 'Nederlands', value: 'nl' },
]

const computedLocale = computed({
  get: () => locale.value,
  set: value => setLocale(value),
})
</script>

<template>
  <USelect
    v-model="computedLocale"
    :items="availableLocales"
    value-key="value"
    label-key="label"
  />
</template>
