<script setup lang="ts">
import type { IntegrationData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'

defineProps<{
  integrations: IntegrationData[]
}>()

const emit = defineEmits<{
  configureIntegration: [integration: IntegrationData]
  deleteIntegration: [integrationId: string]
}>()

const { t } = useI18n()

function configureIntegration(integration: IntegrationData) {
  emit('configureIntegration', integration)
}

async function showDeleteConfirmation(integration: IntegrationData) {
  const overlay = useOverlay().create(ModalsConfirmModal).open({
    title: t('integrations.list.delete_confirmation_title'),
    description: t('integrations.list.delete_confirmation_description', { name: integration.label }),
  })

  if (!await overlay.result)
    return

  emit('deleteIntegration', integration.id)
}
</script>

<template>
  <div>
    <h2 class="font-bold mb-4">
      {{ t('integrations.list.active_title') }}
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="integration in integrations"
        :key="integration.id"
        class="flex items-center justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-1">
          <p class="font-bold flex items-center">
            <slot name="integration-name" :integration="integration">
              {{ integration.label }}
            </slot>
          </p>
          <p class="text-sm text-gray-500">
            <slot name="integration-description" :integration="integration">
              {{ integration.type.toUpperCase() }}
            </slot>
          </p>
        </div>
        <div class="flex gap-2">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            :label="t('common.actions.configure')"
            @click="configureIntegration(integration)"
          />
          <UButton
            variant="link"
            color="primary"
            class="text-sm ml-2 px-0 py-0 h-auto min-h-0"
            :label="t('common.actions.delete')"
            @click="showDeleteConfirmation(integration)"
          />
        </div>
      </div>
      <p v-if="!integrations?.length" class="p-4 text-center text-gray-500">
        {{ t('integrations.list.no_integrations') }}
      </p>
    </UPageCard>
  </div>
</template>
