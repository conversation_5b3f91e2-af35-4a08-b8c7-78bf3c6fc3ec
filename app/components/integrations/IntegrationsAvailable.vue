<script setup lang="ts">
import type { AvailableIntegrationData } from '~/utils/types/api/api'

defineProps<{
  availableIntegrations: Record<string, AvailableIntegrationData>
}>()

const emit = defineEmits<{
  addIntegration: [key: string]
}>()

const { t } = useI18n()

function addIntegration(key: string) {
  emit('addIntegration', key)
}

const descriptionMap: Record<string, string> = {
  api: t('integrations.descriptions.api'),
  ftp: t('integrations.descriptions.ftp'),
}

function getIntegrationDescription(key: string): string {
  return descriptionMap[key] || t('integrations.descriptions.default')
}
</script>

<template>
  <div>
    <h2 class="font-bold mb-4">
      {{ t('integrations.available.title') }}
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="(integration, key) in availableIntegrations"
        :key="key"
        class="flex items-center justify-between not-last:pb-4 gap-2"
      >
        <div class="flex flex-col gap-1 mr-10">
          <p class="g font-bold">
            {{ integration.name }}
          </p>
          <p>
            {{ getIntegrationDescription(key) }}
          </p>
        </div>
        <UButton
          leading-icon="i-ph-plus"
          variant="outline"
          class="py-2 px-4 text-sm"
          :label="t('integrations.available.add_button')"
          @click="addIntegration(key)"
        />
      </div>
      <p v-if="!Object.keys(availableIntegrations).length" class="p-4 text-center text-gray-500">
        {{ t('integrations.available.no_available') }}
      </p>
    </UPageCard>
  </div>
</template>
