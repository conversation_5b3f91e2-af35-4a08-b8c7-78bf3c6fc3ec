<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeTableFieldData } from '~/utils/types/api/api'
import { FormsAssetTypeUpsertAssetTypeTableFieldForm, ModalsConfirmModal } from '#components'
import { useAssetTypeFieldsQuery } from '~/api/asset-type-fields'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()
const { t } = useI18n()

const assetTypeTableFieldStore = useAssetTypeTableFieldStore()
const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const assetTypeRelationship = useAssetTypeRelationshipStore()

function getFieldLabelById(fieldId: string): string {
  try {
    const field = allAssetTypeFields.value?.find(f => f.id === fieldId)
    return field?.label || t('master_data.placeholders.no_field')
  }
  catch {
    return t('master_data.placeholders.no_field')
  }
}

const columns: BaseColumn<AssetTypeTableFieldData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('asset_type_relationship_id', t('master_data.columns.master_data_relationship'), false, (assetTypeTableField) => {
    try {
      return assetTypeRelationship.getById(assetTypeTableField?.asset_type_relationship_id ?? '')?.label
    }
    catch {
      return t('master_data.placeholders.no_relationship')
    }
  }),
  new TextColumn('asset_type_field_id', t('master_data.columns.master_data_field'), false, (assetTypeTableField) => {
    return getFieldLabelById(assetTypeTableField?.asset_type_field_id ?? '')
  }),
  new TextColumn('type', 'Type'),
  new TextColumn('label', 'Label'),
  new TextColumn('weight', t('master_data.columns.weight')),
  new CheckboxColumn('locked', t('master_data.columns.locked')),
  new ActionColumn(t('master_data.columns.actions'), [
    { label: t('common.actions.edit'), onClick: onEdit },
    { label: t('common.actions.delete'), onClick: onDelete },
    { label: t('master_data.actions.move_up'), onClick: onUp },
    { label: t('master_data.actions.move_down'), onClick: onDown },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const formModal = overlay.create(FormsAssetTypeUpsertAssetTypeTableFieldForm)

async function onUp(assetTypeTableField: AssetTypeTableFieldData) {
  await assetTypeTableFieldStore.moveUp(props.assetTypeId, assetTypeTableField.id as string)
  await assetTypeTableFieldStore.hydrate(props.assetTypeId)
}

async function onDown(assetTypeTableField: AssetTypeTableFieldData) {
  await assetTypeTableFieldStore.moveDown(props.assetTypeId, assetTypeTableField.id as string)
  await assetTypeTableFieldStore.hydrate(props.assetTypeId)
}

function onEdit(assetTypeTableField: AssetTypeTableFieldData) {
  formModal.open({
    assetTypeId: props.assetTypeId,
    assetTypeTableField,
  })
}

async function onDelete(assetTypeTableField: AssetTypeTableFieldData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeTableFieldStore.destroy(props.assetTypeId, assetTypeTableField.id as string)
    toast.add({ title: 'Master data table field deleted', color: 'success' })
  }
  catch (e) {
    toast.add({
      title: 'Error deleting master data table field',
      description: (e as FetchError).data?.message || 'An error occurred',
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeTableFieldStore.getByAssetTypeId(assetTypeId)"
  />
</template>
