<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeFieldData } from '~/utils/types/api/api'
import {
  FormsAssetTypeEditAssetTypeFieldForm,
  ModalsConfirmModal,
} from '#components'
import { useAssetTypeFieldsQuery, useDeleteAssetTypeFieldMutation } from '~/api/asset-type-fields'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()
const { t } = useI18n()

const { data: allAssetTypeFields } = useAssetTypeFieldsQuery()
const deleteAssetTypeFieldMutation = useDeleteAssetTypeFieldMutation()

function getFieldsByAssetTypeId(assetTypeId: string) {
  return allAssetTypeFields.value?.filter(field => field.asset_type_id === assetTypeId) || []
}

const columns: BaseColumn<AssetTypeFieldData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('label', 'Label'),
  new TextColumn('field', t('master_data.columns.field')),
  new TextColumn('type', 'Type'),
  new TextColumn('weight', t('master_data.columns.weight')),
  new CheckboxColumn('nullable', 'Nullable'),
  new CheckboxColumn('locked', t('master_data.columns.locked')),
  new CheckboxColumn('searchable', t('master_data.columns.searchable')),
  new CheckboxColumn('required', t('master_data.columns.required')),
  new ActionColumn(t('master_data.columns.actions'), [
    { label: t('common.actions.edit'), onClick: onEdit },
    { label: t('common.actions.delete'), onClick: onDelete },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const editModal = overlay.create(FormsAssetTypeEditAssetTypeFieldForm)

function onEdit(assetTypeField: AssetTypeFieldData) {
  editModal.open({
    assetTypeField,
  })
}

async function onDelete(assetTypeField: AssetTypeFieldData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await deleteAssetTypeFieldMutation.mutateAsync({
      assetTypeId: props.assetTypeId,
      fieldId: assetTypeField.id as string,
    })
    toast.add({ title: t('master_data.messages.field_deleted_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.field_delete_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="getFieldsByAssetTypeId(assetTypeId)"
  />
</template>
