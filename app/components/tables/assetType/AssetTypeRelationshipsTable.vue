<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeRelationshipData } from '~/utils/types/api/api'
import { FormsAssetTypeEditAssetTypeRelationshipForm, ModalsConfirmModal } from '#components'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CheckboxColumn } from '~/utils/table/CheckboxColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()
const { t } = useI18n()

const assetTypeStore = useAssetTypeStore()
const assetTypeRelationshipStore = useAssetTypeRelationshipStore()

const columns: BaseColumn<AssetTypeRelationshipData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('label', 'Label'),
  new TextColumn('relationship_function_name', t('master_data.columns.relationship_function_name')),
  new TextColumn('relationship_method', t('master_data.columns.relationship_method')),
  new TextColumn('relationship_asset_type_id', t('master_data.columns.relationship_asset'), false, (data) => {
    try {
      return assetTypeStore.getById(data.relationship_asset_type_id ?? '')?.label
    }
    catch {
      return t('master_data.placeholders.no_relationship')
    }
  }),
  new CheckboxColumn('include', t('master_data.columns.include')),
  new CheckboxColumn('nullable', 'Nullable'),
  new CheckboxColumn('locked', t('master_data.columns.locked')),
  new CheckboxColumn('cascade_delete', 'Cascade'),
  new CheckboxColumn('required', t('master_data.columns.required')),
  new ActionColumn(t('master_data.columns.actions'), [
    { label: t('common.actions.edit'), onClick: onEdit },
    { label: t('common.actions.delete'), onClick: onDelete },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)
const editSlideover = overlay.create(FormsAssetTypeEditAssetTypeRelationshipForm)

function onEdit(assetTypeRelationship: AssetTypeRelationshipData) {
  editSlideover.open({
    assetTypeRelationship,
  })
}

async function onDelete(assetTypeRelationship: AssetTypeRelationshipData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeRelationshipStore.destroy(props.assetTypeId, assetTypeRelationship.id as string)
    toast.add({ title: t('master_data.messages.relationship_deleted_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.relationship_delete_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeRelationshipStore.getByAssetTypeId(assetTypeId)"
  />
</template>
