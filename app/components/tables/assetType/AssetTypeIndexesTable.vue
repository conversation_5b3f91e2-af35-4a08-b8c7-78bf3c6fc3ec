<script setup lang="ts">
import type { FetchError } from 'ofetch'
import type { BaseColumn } from '~/utils/table/BaseColumn'
import type { AssetTypeIndexData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'
import { ActionColumn } from '~/utils/table/ActionColumn'
import { CopyColumn } from '~/utils/table/CopyColumn'
import { TextColumn } from '~/utils/table/TextColumn'

const props = defineProps({
  assetTypeId: {
    type: String,
    required: true,
  },
})

const overlay = useOverlay()
const toast = useToast()
const { t } = useI18n()

const assetTypeIndexStore = useAssetTypeIndexStore()

const columns: BaseColumn<AssetTypeIndexData>[] = [
  new CopyColumn('id', 'Id'),
  new TextColumn('name', t('common.fields.name')),
  new TextColumn('type', 'Type'),
  new ActionColumn(t('master_data.columns.actions'), [
    { label: t('common.actions.delete'), onClick: onDelete },
  ]),
]
const columnConfig = columns.map(column => column.getConfig())

// Modals
const confirmModal = overlay.create(ModalsConfirmModal)

async function onDelete(assetTypeIndex: AssetTypeIndexData) {
  const overlay = confirmModal.open()

  if (!await overlay.result)
    return

  try {
    await assetTypeIndexStore.destroy(props.assetTypeId, assetTypeIndex.id as string)
    toast.add({ title: t('master_data.messages.index_deleted_success'), color: 'success' })
  }
  catch (e) {
    toast.add({
      title: t('master_data.messages.index_delete_error'),
      description: (e as FetchError).data?.message || t('master_data.messages.generic_error'),
      color: 'error',
    })
  }
}
</script>

<template>
  <UTable
    :key="assetTypeId"
    :columns="columnConfig"
    :data="assetTypeIndexStore.getByAssetTypeId(assetTypeId)"
  />
</template>
