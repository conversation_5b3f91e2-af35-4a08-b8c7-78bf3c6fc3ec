<script setup lang="ts">
import { computed } from 'vue'
import { useUserInfo } from '~/api/user'
import { useLogoutFlow } from '~/composables/auth/useLogoutFlow'
import { useIsHyperfoxUser } from '~/composables/hyperfox/useIsHyperfoxUser'

const { logout } = useLogoutFlow()
const settingsStore = useSettingsStore()
const route = useRoute()
const { t } = useI18n()

const { isHyperfoxUser } = useIsHyperfoxUser()

const items = computed(() => {
  const autoProcess = settingsStore.settings?.autoProcess ?? false
  const baseItems = [
    { label: t('navigation.dashboard'), to: '/' },
    { label: t('navigation.to_process'), to: '/to-process', active: route.path.includes('/to-process') },
    { label: t('navigation.to_validate'), to: '/validate', active: route.path.includes('/validate') },
    { label: t('navigation.archive'), to: '/archive', active: route.path.includes('/archive') },
    { label: t('navigation.reference_data'), to: '/reference-data', active: route.path.includes('/reference-data') },
  ]

  return baseItems
    .filter((item) => {
      if (item.to === '/to-process' && autoProcess)
        return false
      if (item.to === '/reference-data' && !isHyperfoxUser.value)
        return false
      return true
    })
})

const userItems = [
  [{ slot: 'account', type: 'label' }],
  [{
    label: t('auth.logout'),
    icon: 'i-ph-sign-out',
    onSelect: () => {
      logout()
    },
  }],
]

const { data: userInfo } = useUserInfo()
</script>

<template>
  <UHeader class="py-2">
    <template #title>
      <img class="h-8" src="~/assets/img/hyperfox.svg" alt="Hyperfox logo">
    </template>
    <UNavigationMenu
      arrow
      content-orientation="vertical"
      :items="items"
    />
    <template #right>
      <LanguageSelector v-if="isHyperfoxUser" />
      <UButton v-if="isHyperfoxUser" icon="i-ph-gear" to="/admin" />
      <UDropdownMenu :items="userItems">
        <UButton icon="i-ph-user" color="neutral" variant="outline" />
        <template #account>
          <div v-if="userInfo" class="flex flex-col items-start">
            <span class="text-sm font-bold">{{ userInfo.first_name }} {{ userInfo.last_name }}</span>
            <span class="text-sm text-neutral-300">{{ userInfo.email }}</span>
          </div>
        </template>
      </UDropdownMenu>
      <UButton id="hf_chat_button" trailing-icon="i-ph-question-bold">
        Help
      </UButton>
    </template>
  </UHeader>
</template>
