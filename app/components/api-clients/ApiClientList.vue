<script setup lang="ts">
import type { ApiClientData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'

defineProps<{
  apiClients: ApiClientData[]
}>()

const emit = defineEmits<{
  configureApiClient: [apiClient: ApiClientData]
  deleteApiClient: [apiClientId: string]
}>()

const { t } = useI18n()

function configureApiClient(apiClient: ApiClientData) {
  emit('configureApiClient', apiClient)
}

async function showDeleteConfirmation(apiClient: ApiClientData) {
  const overlay = useOverlay().create(ModalsConfirmModal).open({
    title: t('api_clients.list.delete_confirmation_title'),
    description: t('api_clients.list.delete_confirmation_description', { name: apiClient.name }),
  })

  if (!await overlay.result)
    return

  emit('deleteApiClient', apiClient.id)
}
</script>

<template>
  <div>
    <h2 class="text-lg font-bold mb-4">
      {{ t('api_clients.list.active_title') }}
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="apiClient in apiClients"
        :key="apiClient.id"
        class="flex items-center justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-1">
          <p class="font-bold flex items-center">
            <slot name="api-client-name" :api-client="apiClient">
              {{ apiClient.name }}
            </slot>
          </p>
          <div class="flex flex-col gap-1">
            <p class="text-sm text-gray-500">
              API Key: {{ apiClient.key }}
            </p>
            <p class="text-sm text-gray-500">
              {{ t('api_clients.list.scopes_label') }}: <span class="font-bold">{{ apiClient.scopes.join(' , ') }}</span>
            </p>
          </div>
        </div>
        <div class="flex items-center">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            :label="t('common.actions.configure')"
            @click="configureApiClient(apiClient)"
          />
          <UButton
            variant="link"
            color="error"
            class="py-2 px-4 text-sm ml-2"
            :label="t('common.actions.delete')"
            @click="showDeleteConfirmation(apiClient)"
          />
        </div>
      </div>
      <p v-if="!apiClients?.length" class="p-4 text-center text-gray-500">
        {{ t('api_clients.list.no_clients') }}
      </p>
    </UPageCard>
  </div>
</template>
