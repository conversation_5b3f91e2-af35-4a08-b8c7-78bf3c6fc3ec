<script setup lang="ts">
import type { ExportProfileData } from '~/utils/types/api/api'
import { ModalsConfirmModal } from '#components'
import { isExportProfileReady } from '~/utils/export-profiles/export-profile-status.checker'

defineProps<{
  profiles: ExportProfileData[]
}>()

const emit = defineEmits<{
  configureProfile: [profile: ExportProfileData]
  deleteProfile: [profileId: string]
}>()

const { t } = useI18n()

function configureProfile(profile: ExportProfileData) {
  emit('configureProfile', profile)
}

async function showDeleteConfirmation(profile: ExportProfileData) {
  const overlay = useOverlay().create(ModalsConfirmModal).open({
    title: t('export_profiles.list.delete_confirmation_title'),
    description: t('export_profiles.list.delete_confirmation_description', { name: profile.name }),
  })

  if (!await overlay.result)
    return

  emit('deleteProfile', profile.id)
}
</script>

<template>
  <div>
    <h2 class="font-bold mb-4">
      {{ t('export_profiles.list.profiles_title') }}
    </h2>
    <UPageCard variant="outline" class="shadow" :ui="{ container: 'divide-y divide-(--ui-border)' }">
      <div
        v-for="profile in profiles"
        :key="profile.id"
        class="flex items-start justify-between py-4 first:pt-0 last:pb-0"
      >
        <div class="flex flex-col gap-2">
          <div class="flex gap-2">
            <p class="font-bold flex items-center">
              <slot name="profile-name" :profile="profile">
                {{ profile.name }}
              </slot>
            </p>
          </div>
          <div
            v-if="!isExportProfileReady(profile)"
            class="flex items-start gap-2 text-sm text-gray-500"
          >
            <UIcon name="i-lucide-info" class="bg-error-500 w-[18px] h-[18px] flex-shrink-0" />
            <p>
              {{ t('export_profiles.list.not_ready_message') }}
            </p>
          </div>
          <div v-else class="flex flex-col gap-1">
            <p
              class="text-sm text-gray-500"
            >
              <slot name="profile-description" :profile="profile">
                {{ t('export_profiles.list.ready_message') }}
              </slot>
            </p>
          </div>
        </div>
        <div class="flex gap-2 ml-10">
          <UButton
            icon="i-heroicons-cog-6-tooth"
            variant="outline"
            class="py-2 px-4 text-sm"
            :label="t('common.actions.configure')"
            @click="configureProfile(profile)"
          />
          <UButton
            variant="link"
            color="primary"
            class="text-sm ml-2 px-0 py-0 h-auto min-h-0"
            :label="t('common.actions.delete')"
            @click="showDeleteConfirmation(profile)"
          />
        </div>
      </div>
      <p v-if="!profiles?.length" class="p-4 text-center text-gray-500">
        {{ t('export_profiles.list.no_profiles') }}
      </p>
    </UPageCard>
  </div>
</template>
