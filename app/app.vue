<script setup lang="ts">
import { useTenantStore } from '~/stores/tenant.store'
import VueQueryDevToolsWrapper from './components/devtools/VueQueryDevToolsWrapper.vue'
import { useIntercom } from './composables/intercom/useIntercom'

const tenantStore = useTenantStore()
const { t } = useI18n()

const name = computed<string>(() => {
  return tenantStore.tenant?.name ?? ''
})

const titlePrefix = computed(() => {
  const tenantName = name.value
  if (tenantName.length === 0) {
    return 'Hyperfox'
  }
  return `Hyperfox - ${tenantName}`
})

useHead({
  titleTemplate: (titleChunk) => {
    const prefix = titlePrefix.value
    const suffix = titleChunk ?? t('common.app_subtitle')
    return `${prefix} - ${suffix}`
  },
})

const { setupIntercomWithUserData } = useIntercom()

onMounted(async () => {
  await setupIntercomWithUserData()
})
</script>

<template>
  <NuxtLoadingIndicator color="#ff2600" />
  <UApp>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <VueQueryDevToolsWrapper />
  </UApp>
</template>
