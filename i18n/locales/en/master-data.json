{"master_data": {"title": "Master data", "add_button": "Add master data", "detail_title": "Master data: {label}", "tabs": {"fields": "Fields", "relationships": "Relationships", "indexes": "Indexes", "form_fields": "Form fields", "table_fields": "Table fields"}, "buttons": {"add_fields": "Add Fields", "add_relationships": "Add Relationships", "add_indexes": "Add Indexes", "add_formFields": "Add Form fields", "add_tableFields": "Add Table fields", "generate": "generate"}, "columns": {"table_name": "Table name", "display_field": "Display field", "reference_data": "Reference data", "actions": "Actions", "field": "Field", "weight": "Weight", "locked": "Locked", "searchable": "Searchable", "required": "Required", "relationship_function_name": "Name", "relationship_method": "Method", "relationship_asset": "Relationship Asset", "include": "Include", "master_data_relationship": "Master data relationship", "master_data_field": "Master data field"}, "actions": {"clear_data": "Clear data", "move_up": "Move up", "move_down": "Move down"}, "placeholders": {"no_field": "No field", "no_relationship": "No relationship"}, "forms": {"add_field_title": "Add field", "edit_field_title": "Update field: {label}", "add_relationship_title": "Add relationship", "add_index_title": "Add index", "add_form_field_title": "Add form field", "edit_form_field_title": "Edit form field", "add_table_field_title": "Add table field", "edit_table_field_title": "Edit table field", "add_master_data_title": "Add master data", "edit_master_data_title": "Edit master data", "edit_relationship_title": "Update relationship: {label}", "labels": {"weight": "Weight", "locked": "Locked", "required": "Required", "searchable": "Searchable", "precision": "Precision", "display_field": "Display field", "reference_data": "Reference data", "auditing_enabled": "Auditing enabled", "select_form": "Select form", "select_relatable_type": "Select relatable type", "select_field": "Select field", "select_relationship": "Select relationship", "master_data": "Master data", "add_field_to_template": "Add field to template", "template": "Template", "nullable": "Nullable", "include": "Include", "cascade_delete": "Cascade delete", "master_data_relationship": "Master data relationship", "select_type": "Select type", "filterable": "Filterable", "suffix": "Suffix"}, "placeholders": {"enter_label_name": "Enter a label name", "select_form": "Select form", "select_span_size": "Select span size", "select_field": "Select field", "select_master_data": "Select master data", "select_foreign_relationship": "Select a foreign relationship", "select_type": "Select type", "select_relationship": "Select relationship", "suffix_placeholder": "e.g. %, kg, m², etc."}, "buttons": {"create": "Create", "update": "Update"}}, "messages": {"deleted_success": "Master data deleted", "delete_error": "Could not delete master data", "data_truncated": "Reference data truncated", "truncate_error": "Could not truncate data", "generic_error": "An error occurred", "field_deleted_success": "Master data field deleted", "field_delete_error": "Error deleting master data field", "field_created_success": "Master data field created", "field_create_error": "Error creating master data field", "field_updated_success": "Master data field updated", "field_update_error": "Error updating master data field", "relationship_deleted_success": "Master data relationship deleted", "relationship_delete_error": "Error deleting master data relationship", "index_deleted_success": "Master data index deleted", "index_delete_error": "Error deleting master data field index", "form_fields_generated_success": "Form fields generated successfully", "form_fields_generate_error": "Error generating form fields", "form_field_created_success": "Master data form field created", "form_field_updated_success": "Master data form field updated", "form_field_upsert_error": "Error {action} master data form field", "master_data_updated_success": "Master data updated", "master_data_update_error": "Error updating master data field", "master_data_created_success": "Master data created", "master_data_create_error": "Error creating master data field", "index_created_success": "Master data index created", "index_create_error": "Error creating master data index", "relationship_created_success": "Master data relationship created", "relationship_create_error": "Error creating master data relationship", "relationship_updated_success": "Master data relationship updated", "relationship_update_error": "Error updating master data relationship", "form_field_create_error": "Error creating master data form field", "form_field_update_error": "Error updating master data form field", "table_field_created_success": "Created master data table field", "table_field_updated_success": "Updated master data table field", "table_field_create_error": "Error creating master data table field", "table_field_update_error": "Error updating master data table field"}}}